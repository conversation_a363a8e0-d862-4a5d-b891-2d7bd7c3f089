<template>
  <div class="centering-test">
    <h1>居中效果测试页面</h1>

    <!-- 测试不同状态的居中效果 -->
    <div class="test-section">
      <h2>空状态居中测试</h2>
      <div class="test-container">
        <div class="graph-content">
          <div class="graph-container">
            <LoadingState
              :empty="true"
              empty-title="数据血缘图谱"
              empty-message="请在左侧输入 SQL 语句并点击『解析血缘』按钮开始分析"
              :empty-actions="emptyActions"
            />
          </div>
        </div>
      </div>
      <p class="test-note">✅ 检查点：内容应该在容器中完美水平和垂直居中</p>
    </div>

    <div class="test-section">
      <h2>加载状态居中测试</h2>
      <div class="test-container">
        <div class="graph-content">
          <div class="graph-container">
            <LoadingState
              :loading="true"
              loading-text="正在解析血缘关系..."
              :show-progress="true"
              :progress="65"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>错误状态居中测试</h2>
      <div class="test-container">
        <div class="graph-content">
          <div class="graph-container">
            <LoadingState
              :error="true"
              error-title="解析失败"
              error-message="SQL 语句解析过程中遇到错误，请检查语法后重试"
              :show-retry="true"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 不同尺寸测试 -->
    <div class="test-section">
      <h2>不同容器尺寸测试</h2>

      <h3>小容器 (300x200)</h3>
      <div class="test-container small">
        <div class="graph-content">
          <div class="graph-container">
            <LoadingState
              :empty="true"
              empty-title="小容器测试"
              empty-message="测试在小容器中的居中效果"
            />
          </div>
        </div>
      </div>

      <h3>大容器 (800x600)</h3>
      <div class="test-container large">
        <div class="graph-content">
          <div class="graph-container">
            <LoadingState
              :empty="true"
              empty-title="大容器测试"
              empty-message="测试在大容器中的居中效果"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 响应式测试说明 -->
    <div class="test-section">
      <h2>响应式测试说明</h2>
      <p>请调整浏览器窗口大小来测试响应式居中效果：</p>
      <ul>
        <li>桌面端 (>1200px): 最大宽度 500px</li>
        <li>平板端 (768px-1200px): 最大宽度 400px</li>
        <li>手机端 (480px-768px): 最大宽度 300px，减少内边距</li>
        <li>小屏手机 (<480px): 最大宽度 280px，图标尺寸调整</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LoadingState from '@/components/LoadingState.vue'

// 空状态操作按钮
const emptyActions = ref([
  {
    key: 'sample',
    text: '加载示例数据',
    type: 'primary' as const,
    icon: 'ShareAltOutlined',
    handler: () => {
      console.log('加载示例数据')
    }
  },
  {
    key: 'help',
    text: '查看帮助',
    type: 'default' as const,
    icon: 'QuestionCircleOutlined',
    handler: () => {
      console.log('查看帮助')
    }
  }
])
</script>

<style scoped>
.centering-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 48px;
}

.test-section h1 {
  color: #1890ff;
  text-align: center;
  margin-bottom: 32px;
}

.test-section h2 {
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.test-section h3 {
  color: #595959;
  margin: 16px 0 8px 0;
}

.test-container {
  width: 100%;
  height: 400px;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  margin-bottom: 24px;
  position: relative;
  background: #fafafa;
  /* 添加网格背景来更好地观察居中效果 */
  background-image:
    linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.test-container.small {
  width: 300px;
  height: 200px;
}

.test-container.large {
  width: 800px;
  height: 600px;
}

/* 复制 LineageLayout.vue 中的样式 */
.graph-content {
  padding: 0;
  background: var(--color-background, #ffffff);
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.graph-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--color-background, #ffffff);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.test-section ul {
  margin: 16px 0;
  padding-left: 24px;
}

.test-section li {
  margin-bottom: 8px;
  color: #595959;
}

.test-section p {
  color: #595959;
  line-height: 1.6;
}

.test-note {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
  font-size: 14px;
  color: #389e0d;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .test-container {
    background: #1f1f1f;
    border-color: #434343;
  }

  .graph-content,
  .graph-container {
    background: #1f1f1f;
  }

  .test-section h2 {
    color: #f0f0f0;
    border-bottom-color: #434343;
  }

  .test-section h3 {
    color: #bfbfbf;
  }

  .test-section p,
  .test-section li {
    color: #bfbfbf;
  }
}
</style>
