<template>
  <div class="lineage-layout">
    <!-- 主布局容器 -->
    <a-layout class="layout-container">
      <!-- 左侧面板 -->
      <a-layout-sider
        v-model:collapsed="leftCollapsed"
        :width="leftPanelWidth"
        :collapsed-width="0"
        :trigger="null"
        collapsible
        class="left-panel"
        theme="light"
      >
        <div class="left-panel-content">
          <!-- 工具栏区域 -->
          <div class="toolbar-section">
            <div class="toolbar-header">
              <h3>SQL 血缘分析</h3>
              <a-button
                type="text"
                :icon="leftCollapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
                @click="toggleLeftPanel"
                class="collapse-btn"
              />
            </div>

            <!-- 数据库类型选择 -->
            <div class="control-group">
              <label class="control-label">数据库类型</label>
              <a-select
                v-model:value="selectedDbType"
                placeholder="选择数据库类型"
                style="width: 100%"
                @change="handleDbTypeChange"
              >
                <a-select-option value="mysql">MySQL</a-select-option>
                <a-select-option value="postgresql">PostgreSQL</a-select-option>
                <a-select-option value="oracle">Oracle</a-select-option>
                <a-select-option value="sqlserver">SQL Server</a-select-option>
                <a-select-option value="hive">Hive</a-select-option>
                <a-select-option value="spark">Spark SQL</a-select-option>
              </a-select>
            </div>

            <!-- 操作按钮组 -->
            <div class="control-group">
              <a-button type="primary" block @click="handleParseLineage" size="large">
                <template #icon><ShareAltOutlined /></template>
                解析血缘关系
              </a-button>
            </div>
          </div>

          <!-- SQL编辑器区域 -->
          <div class="editor-section">
            <SqlEditor
              v-model="sqlContent"
              :database-type="selectedDbType"
              :theme="theme"
              placeholder="请输入 SQL 语句..."
              @change="handleSqlChange"
              @format="handleSqlFormat"
              @clear="handleClearSql"
            />
          </div>
        </div>
      </a-layout-sider>

      <!-- 右侧主内容区域 -->
      <a-layout class="right-layout">
        <!-- 右侧头部控制栏 -->
        <a-layout-header class="right-header">
          <div class="header-content">
            <div class="header-left">
              <h2>数据血缘图谱</h2>
            </div>
            <div class="header-right">
              <!-- 控制开关 -->
              <a-space>
                <div class="control-item">
                  <label>字段级血缘</label>
                  <a-switch
                    v-model:checked="showFieldLevel"
                    @change="handleFieldLevelChange"
                    size="small"
                  />
                </div>
                <div class="control-item">
                  <label>完整链路</label>
                  <a-switch
                    v-model:checked="showFullLineage"
                    @change="handleFullLineageChange"
                    size="small"
                  />
                </div>
                <a-divider type="vertical" />
                <!-- 字段筛选控制 -->
                <a-dropdown :trigger="['click']">
                  <a-button type="text" size="small">
                    <FilterOutlined />
                    字段筛选
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="filter-dialog" @click="showFieldFilter = true">
                        <SettingOutlined />
                        筛选设置
                      </a-menu-item>
                      <a-menu-item key="clear-filter" @click="clearFieldFilter">
                        <ClearOutlined />
                        清除筛选
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-divider type="vertical" />
                <!-- 主题切换 -->
                <a-tooltip title="切换主题">
                  <a-button
                    type="text"
                    @click="toggleTheme"
                    size="small"
                  >
                    {{ theme === 'light' ? '🌙' : '☀️' }}
                  </a-button>
                </a-tooltip>

                <!-- 配置管理 -->
                <a-tooltip title="配置管理">
                  <a-button
                    type="text"
                    @click="showConfigPanel = true"
                    size="small"
                  >
                    <SettingOutlined />
                  </a-button>
                </a-tooltip>
                <a-divider type="vertical" />
                <!-- 图谱工具按钮 -->
                <a-tooltip title="适应画布">
                  <a-button
                    type="text"
                    :icon="h(ExpandOutlined)"
                    @click="handleFitView"
                    size="small"
                  />
                </a-tooltip>
                <a-tooltip title="重置布局">
                  <a-button
                    type="text"
                    :icon="h(ReloadOutlined)"
                    @click="handleResetLayout"
                    size="small"
                  />
                </a-tooltip>
                <a-tooltip title="导出图片">
                  <a-button
                    type="text"
                    :icon="h(DownloadOutlined)"
                    @click="handleExportImage"
                    size="small"
                  />
                </a-tooltip>
                <a-tooltip title="切换缩略图">
                  <a-button
                    type="text"
                    :icon="h(BorderOutlined)"
                    @click="handleToggleMiniMap"
                    size="small"
                  />
                </a-tooltip>
                <a-divider type="vertical" />
                <!-- 性能模式控制 -->
                <a-dropdown :trigger="['click']">
                  <a-button type="text" size="small">
                    <ThunderboltOutlined />
                    性能模式
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handlePerformanceModeChange">
                      <a-menu-item key="normal">
                        <CheckCircleOutlined />
                        标准模式
                      </a-menu-item>
                      <a-menu-item key="optimized">
                        <RocketOutlined />
                        优化模式
                      </a-menu-item>
                      <a-menu-item key="extreme">
                        <ThunderboltOutlined />
                        极速模式
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-divider type="vertical" />
                <!-- 布局方向控制 -->
                <a-dropdown :trigger="['click']">
                  <a-button type="text" size="small">
                    布局方向
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleLayoutDirectionChange">
                      <a-menu-item key="LR">
                        <ArrowRightOutlined />
                        从左到右
                      </a-menu-item>
                      <a-menu-item key="TB">
                        <ArrowDownOutlined />
                        从上到下
                      </a-menu-item>
                      <a-menu-item key="RL">
                        <ArrowLeftOutlined />
                        从右到左
                      </a-menu-item>
                      <a-menu-item key="BT">
                        <ArrowUpOutlined />
                        从下到上
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-button
                  type="text"
                  :icon="h(SettingOutlined)"
                  @click="showSettings = true"
                  size="small"
                >
                  设置
                </a-button>
              </a-space>
            </div>
          </div>
        </a-layout-header>

        <!-- 图谱内容区域 -->
        <a-layout-content class="graph-content">
          <div class="graph-container" ref="graphContainer">
            <!-- 搜索框 -->
            <div class="graph-search" v-if="hasGraphData">
              <a-input
                v-model:value="searchKeyword"
                placeholder="搜索表名或字段名..."
                :prefix="h(SearchOutlined)"
                @change="handleSearch"
                @pressEnter="handleSearchEnter"
                class="search-input"
                size="small"
              />
              <div class="search-results" v-if="searchResults.length > 0">
                <div
                  v-for="result in searchResults"
                  :key="result.id"
                  class="search-result-item"
                  @click="handleSearchResultClick(result)"
                >
                  <div class="result-type">{{ result.type === 'table' ? '表' : '字段' }}</div>
                  <div class="result-content">
                    <div class="result-name">{{ result.name }}</div>
                    <div class="result-desc" v-if="result.tableName">{{ result.tableName }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 浮动工具栏 -->
            <div class="graph-toolbar" v-if="hasGraphData">
              <a-space direction="vertical">
                <a-tooltip title="放大" placement="left">
                  <a-button
                    type="primary"
                    :icon="h(ZoomInOutlined)"
                    @click="handleZoomIn"
                    size="small"
                    shape="circle"
                  />
                </a-tooltip>
                <a-tooltip title="缩小" placement="left">
                  <a-button
                    type="primary"
                    :icon="h(ZoomOutOutlined)"
                    @click="handleZoomOut"
                    size="small"
                    shape="circle"
                  />
                </a-tooltip>
                <a-tooltip title="适应画布" placement="left">
                  <a-button
                    type="primary"
                    :icon="h(ExpandOutlined)"
                    @click="handleFitView"
                    size="small"
                    shape="circle"
                  />
                </a-tooltip>
              </a-space>
            </div>

            <!-- 图谱渲染区域 -->
            <div class="graph-canvas" ref="graphCanvas" v-if="hasGraphData && !lineageStore.loading && !lineageStore.error">
              <ErrorBoundary
                fallback-title="图谱渲染失败"
                fallback-message="图谱组件遇到了问题，请尝试重新加载数据"
                @error="handleGraphError"
                @retry="handleGraphRetry"
              >
                <LineageGraph
                  ref="lineageGraphRef"
                  :data="lineageStore.g6GraphData"
                  :width="graphCanvasWidth"
                  :height="graphCanvasHeight"
                  @graph-ready="handleGraphReady"
                  @node-click="handleNodeClick"
                  @edge-click="handleEdgeClick"
                  @canvas-click="handleCanvasClick"
                  @field-hover="handleFieldHover"
                  @field-click="handleFieldClick"
                  @field-leave="handleFieldLeave"
                />
              </ErrorBoundary>
            </div>

            <!-- 统一状态管理 -->
            <LoadingState
              v-if="!hasGraphData || lineageStore.loading || lineageStore.error"
              :loading="lineageStore.loading"
              :loading-text="lineageStore.loadingText"
              :loading-details="lineageStore.loadingSteps.length > 0"
              :loading-steps="lineageStore.loadingSteps"
              :show-progress="lineageStore.loadingProgress !== undefined"
              :progress="lineageStore.loadingProgress"
              :show-cancel="lineageStore.loading"

              :error="!!lineageStore.error"
              :error-title="getErrorTitle()"
              :error-message="lineageStore.error || ''"
              :error-details="lineageStore.errorDetails"
              :show-error-details="isDevelopment"
              :show-retry="lineageStore.retryCount < lineageStore.maxRetries"
              :show-report="lineageStore.errorType !== 'validation'"

              :empty="!lineageStore.loading && !lineageStore.error && !hasGraphData"
              empty-title="数据血缘图谱"
              empty-message="请在左侧输入 SQL 语句并点击『解析血缘』按钮开始分析"
              :empty-actions="emptyActions"

              @cancel="handleCancelLoading"
              @retry="handleRetryOperation"
              @report="handleReportError"
              @dismiss="handleDismissError"
              @empty-action="handleEmptyAction"
            />
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>

    <!-- 字段筛选对话框 -->
    <a-modal
      v-model:open="showFieldFilter"
      title="字段筛选设置"
      :width="600"
      @ok="applyFieldFilter"
      @cancel="cancelFieldFilter"
    >
      <div class="field-filter-content">
        <div class="filter-section">
          <h4>按数据类型筛选</h4>
          <a-checkbox-group v-model:value="fieldFilter.dataTypes" class="filter-checkbox-group">
            <a-checkbox value="string">字符串类型</a-checkbox>
            <a-checkbox value="number">数字类型</a-checkbox>
            <a-checkbox value="date">日期类型</a-checkbox>
            <a-checkbox value="boolean">布尔类型</a-checkbox>
            <a-checkbox value="json">JSON类型</a-checkbox>
            <a-checkbox value="other">其他类型</a-checkbox>
          </a-checkbox-group>
        </div>

        <div class="filter-section">
          <h4>按字段属性筛选</h4>
          <a-checkbox-group v-model:value="fieldFilter.attributes" class="filter-checkbox-group">
            <a-checkbox value="primary">主键字段</a-checkbox>
            <a-checkbox value="nullable">可空字段</a-checkbox>
            <a-checkbox value="indexed">索引字段</a-checkbox>
            <a-checkbox value="foreign">外键字段</a-checkbox>
          </a-checkbox-group>
        </div>

        <div class="filter-section">
          <h4>按表名筛选</h4>
          <a-select
            v-model:value="fieldFilter.tables"
            mode="multiple"
            placeholder="选择要显示的表"
            style="width: 100%"
            :options="tableOptions"
            show-search
            :filter-option="filterTableOption"
          />
        </div>

        <div class="filter-section">
          <h4>按字段名筛选</h4>
          <a-input
            v-model:value="fieldFilter.fieldNamePattern"
            placeholder="输入字段名模式（支持正则表达式）"
            @pressEnter="applyFieldFilter"
          />
          <div class="filter-help">
            <small>例如：^id$（精确匹配id）、.*name.*（包含name）、^user_.*（以user_开头）</small>
          </div>
        </div>

        <div class="filter-section">
          <h4>筛选统计</h4>
          <div class="filter-stats">
            <a-statistic title="匹配字段数" :value="filteredFieldCount" />
            <a-statistic title="总字段数" :value="totalFieldCount" />
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 设置抽屉 -->
    <a-drawer
      v-model:open="showSettings"
      title="图谱设置"
      placement="right"
      :width="320"
    >
      <div class="settings-content">
        <div class="setting-group">
          <h4>布局设置</h4>
          <div class="setting-item">
            <label>布局方向</label>
            <a-radio-group v-model:value="layoutDirection">
              <a-radio value="LR">从左到右</a-radio>
              <a-radio value="TB">从上到下</a-radio>
              <a-radio value="RL">从右到左</a-radio>
              <a-radio value="BT">从下到上</a-radio>
            </a-radio-group>
          </div>
        </div>

        <div class="setting-group">
          <h4>显示设置</h4>
          <div class="setting-item">
            <label>显示字段类型</label>
            <a-switch v-model:checked="showFieldTypes" />
          </div>
          <div class="setting-item">
            <label>显示表注释</label>
            <a-switch v-model:checked="showTableComments" />
          </div>
          <div class="setting-item">
            <label>显示字段描述</label>
            <a-switch v-model:checked="showFieldDescriptions" />
          </div>
          <div class="setting-item">
            <label>显示数据类型</label>
            <a-switch v-model:checked="showDataTypes" />
          </div>
        </div>

        <div class="setting-group">
          <h4>主题设置</h4>
          <div class="setting-item">
            <label>主题模式</label>
            <a-radio-group v-model:value="theme" @change="handleThemeChange">
              <a-radio value="light">浅色主题</a-radio>
              <a-radio value="dark">深色主题</a-radio>
            </a-radio-group>
          </div>
        </div>

        <div class="setting-group">
          <h4>性能设置</h4>
          <div class="setting-item">
            <label>性能模式</label>
            <a-select v-model:value="performanceMode" style="width: 120px" @change="handlePerformanceModeChange">
              <a-select-option value="normal">标准模式</a-select-option>
              <a-select-option value="optimized">优化模式</a-select-option>
              <a-select-option value="extreme">极速模式</a-select-option>
            </a-select>
          </div>
          <div class="setting-item">
            <label>虚拟渲染</label>
            <a-switch v-model:checked="enableVirtualRendering" />
          </div>
        </div>

        <div class="setting-group">
          <h4>配置管理</h4>
          <div class="setting-actions">
            <a-space direction="vertical" style="width: 100%">
              <a-button type="primary" @click="saveCurrentConfig" block>
                <SettingOutlined />
                保存当前配置
              </a-button>
              <a-button @click="loadConfigFromLocal" block>
                <ReloadOutlined />
                加载本地配置
              </a-button>
              <a-button @click="exportConfig" block>
                <DownloadOutlined />
                导出配置文件
              </a-button>
              <a-button @click="importConfig" block>
                <UploadOutlined />
                导入配置文件
              </a-button>
              <a-button @click="configManager.resetToDefault(); loadConfigFromLocal()" block danger>
                <ClearOutlined />
                重置为默认
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 字段详情面板 -->
    <FieldDetailDrawer
      v-model:visible="showFieldDetail"
      :field-data="selectedFieldData"
      @trace-lineage="handleTraceLineage"
      @highlight-field="handleHighlightField"
      @field-click="handleRelatedFieldClick"
    />

    <!-- 配置管理面板 -->
    <ConfigPanel
      v-model:visible="showConfigPanel"
      @config-changed="handleConfigChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, h } from 'vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ShareAltOutlined,
  SettingOutlined,
  ExpandOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SearchOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  BorderOutlined,
  DownOutlined,
  ArrowRightOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowUpOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  RocketOutlined,
  FilterOutlined,
  ClearOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { useLineageStore } from '@/stores/lineageStore'
import SqlEditor from './SqlEditor.vue'
import LineageGraph from './LineageGraph.vue'
import FieldDetailDrawer from './FieldDetailDrawer.vue'
import ConfigPanel from './ConfigPanel.vue'
import ErrorBoundary from './ErrorBoundary.vue'
import LoadingState from './LoadingState.vue'
import { runFieldEdgeTests } from '@/tests/fieldEdgeTest'
import { runAllFieldEdgeTests } from '@/tests/fieldEdgeUnitTest'
import { runCompleteDemo } from '@/tests/fieldEdgeDemo'
import { runFieldInteractionDemo, demoFieldInteractionEvents, demoFieldHighlight } from '@/tests/fieldInteractionDemo'
import type { LineageNode } from '@/types/lineage'
import { configManager, configUtils } from '@/utils/configManager'
import { defaultApiClient } from '@/utils/apiClient'
import { handleError, ErrorType } from '@/utils/errorManager'

// 状态管理
const lineageStore = useLineageStore()

// 响应式数据
const leftCollapsed = ref(false)
const leftPanelWidth = ref(400)
const selectedDbType = ref('mysql')
const sqlContent = ref('')
const showFieldLevel = computed(() => lineageStore.showFieldLevelLineage)
const showFullLineage = computed(() => lineageStore.showCompleteLineage)
const showSettings = ref(false)
const layoutDirection = ref('LR')
const showFieldTypes = ref(true)
const showTableComments = ref(true)
const showFieldDescriptions = ref(true)
const showDataTypes = ref(true)
const theme = ref<'light' | 'dark'>('light')
const searchKeyword = ref('')
const performanceMode = ref<'normal' | 'optimized' | 'extreme'>('normal')
const enableVirtualRendering = ref(false)

// 字段筛选相关状态
const showFieldFilter = ref(false)
const fieldFilter = reactive({
  dataTypes: [] as string[],
  attributes: [] as string[],
  tables: [] as string[],
  fieldNamePattern: ''
})
const filteredFieldCount = ref(0)
const totalFieldCount = ref(0)

// 字段交互相关状态
const showFieldDetail = ref(false)
const selectedFieldData = ref<LineageNode | null>(null)

// 配置管理相关状态
const showConfigPanel = ref(false)

// 性能优化相关状态
const currentPerformanceMode = ref<'normal' | 'optimized' | 'extreme'>('normal')
const performanceStats = reactive({
  nodeCount: 0,
  edgeCount: 0,
  renderTime: 0,
  memoryUsage: 0
})

// DOM引用
const graphContainer = ref<HTMLElement>()
const graphCanvas = ref<HTMLElement>()
const lineageGraphRef = ref<any>(null)

// 计算属性
const hasGraphData = computed(() => {
  return lineageStore.g6GraphData !== null && lineageStore.g6GraphData.nodes.length > 0
})

const loading = computed(() => lineageStore.loading)

// 错误处理相关计算属性
const getErrorTitle = () => {
  switch (lineageStore.errorType) {
    case 'network': return '网络连接错误'
    case 'validation': return '数据验证错误'
    case 'business': return '业务处理错误'
    case 'system': return '系统错误'
    default: return '未知错误'
  }
}

// 空状态操作按钮
const emptyActions = computed(() => [
  {
    key: 'sample',
    text: '加载示例数据',
    type: 'primary' as const,
    icon: 'ShareAltOutlined',
    handler: handleLoadSample
  },
  {
    key: 'help',
    text: '查看帮助',
    type: 'default' as const,
    icon: 'QuestionCircleOutlined',
    handler: () => {
      // 打开帮助文档
      console.log('打开帮助文档')
    }
  }
])

// 开发环境检测
const isDevelopment = computed(() => import.meta.env.DEV)

// 图谱画布尺寸计算
const graphCanvasWidth = computed(() => {
  if (!graphCanvas.value) return 800
  // 确保获取到有效的宽度值
  const width = graphCanvas.value.clientWidth || graphCanvas.value.offsetWidth || 800
  return Math.max(width, 400) // 最小宽度400px
})

const graphCanvasHeight = computed(() => {
  if (!graphCanvas.value) return 600
  // 确保获取到有效的高度值
  const height = graphCanvas.value.clientHeight || graphCanvas.value.offsetHeight || 600
  return Math.max(height, 300) // 最小高度300px
})

const searchResults = computed(() => {
  return lineageStore.searchResults
})

// 表选项计算属性
const tableOptions = computed(() => {
  if (!lineageStore.lineageData) return []
  return Object.keys(lineageStore.lineageData.tables).map(tableName => ({
    label: tableName,
    value: tableName
  }))
})

// 更新字段统计
const updateFieldStats = () => {
  if (!lineageStore.lineageData) {
    totalFieldCount.value = 0
    filteredFieldCount.value = 0
    return
  }

  totalFieldCount.value = lineageStore.lineageData.nodes.length

  // 计算筛选后的字段数量
  let filtered = lineageStore.lineageData.nodes

  // 按数据类型筛选
  if (fieldFilter.dataTypes.length > 0) {
    filtered = filtered.filter(node => {
      const dataType = node.dataType?.type?.toLowerCase() || 'other'
      return fieldFilter.dataTypes.some(type => {
        switch (type) {
          case 'string': return ['varchar', 'char', 'text', 'string'].some(t => dataType.includes(t))
          case 'number': return ['int', 'decimal', 'float', 'double', 'number'].some(t => dataType.includes(t))
          case 'date': return ['date', 'time', 'timestamp'].some(t => dataType.includes(t))
          case 'boolean': return ['bool', 'boolean'].some(t => dataType.includes(t))
          case 'json': return ['json', 'jsonb'].some(t => dataType.includes(t))
          case 'other': return !['varchar', 'char', 'text', 'string', 'int', 'decimal', 'float', 'double', 'number', 'date', 'time', 'timestamp', 'bool', 'boolean', 'json', 'jsonb'].some(t => dataType.includes(t))
          default: return false
        }
      })
    })
  }

  // 按字段属性筛选
  if (fieldFilter.attributes.length > 0) {
    filtered = filtered.filter(node => {
      return fieldFilter.attributes.some(attr => {
        switch (attr) {
          case 'primary': return node.isKey
          case 'nullable': return node.isNullable
          case 'indexed': return false // 暂时不支持
          case 'foreign': return false // 暂时不支持
          default: return false
        }
      })
    })
  }

  // 按表名筛选
  if (fieldFilter.tables.length > 0) {
    filtered = filtered.filter(node => fieldFilter.tables.includes(node.tableName))
  }

  // 按字段名模式筛选
  if (fieldFilter.fieldNamePattern.trim()) {
    try {
      const regex = new RegExp(fieldFilter.fieldNamePattern, 'i')
      filtered = filtered.filter(node => regex.test(node.fieldName))
    } catch (e) {
      // 如果正则表达式无效，使用简单的包含匹配
      const pattern = fieldFilter.fieldNamePattern.toLowerCase()
      filtered = filtered.filter(node => node.fieldName.toLowerCase().includes(pattern))
    }
  }

  filteredFieldCount.value = filtered.length
}

// 方法
const toggleLeftPanel = () => {
  leftCollapsed.value = !leftCollapsed.value
}

const handleDbTypeChange = (value: string) => {
  console.log('数据库类型变更:', value)
  lineageStore.setDatabaseType(value as any)
}

const handleParseLineage = async () => {
  if (!sqlContent.value.trim()) {
    message.warning('请先输入 SQL 语句')
    return
  }

  try {
    await lineageStore.parseSqlLineage(sqlContent.value)
  } catch (error) {
    handleError(error, ErrorType.BUSINESS, {
      showNotification: true,
      context: { sql: sqlContent.value }
    })
  }
}

const handleClearSql = () => {
  sqlContent.value = ''
  message.info('已清空 SQL 内容')
}

const handleSqlChange = (value: string) => {
  // SQL内容变化处理
  console.log('SQL内容变化:', value)
  lineageStore.sqlText = value
}

const handleSqlFormat = (formattedSql: string) => {
  console.log('SQL格式化完成:', formattedSql)
  sqlContent.value = formattedSql
}

const handleFieldLevelChange = (checked: boolean) => {
  console.log('字段级血缘关系:', checked)
  if (checked !== lineageStore.showFieldLevelLineage) {
    lineageStore.toggleFieldLevelLineage()
  }
}

const handleFullLineageChange = (checked: boolean) => {
  console.log('完整血缘链路:', checked)
  if (checked !== lineageStore.showCompleteLineage) {
    lineageStore.toggleCompleteLineage()
  }
}

// 图谱控制方法
const handleFitView = () => {
  console.log('适应画布')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.fitView()
    message.success('已适应画布大小')
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleResetLayout = () => {
  console.log('重置布局')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    const success = lineageGraphRef.value.resetLayout()
    if (success) {
      message.success('已重置图谱布局')
    } else {
      message.error('重置布局失败')
    }
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleToggleMiniMap = () => {
  console.log('切换缩略图')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.toggleMiniMap()
    const isVisible = lineageGraphRef.value.showMiniMap
    message.success(`缩略图已${isVisible ? '显示' : '隐藏'}`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleExportImage = () => {
  console.log('导出图片')

  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    // 显示导出选项对话框
    Modal.confirm({
      title: '导出图谱',
      content: '请选择导出格式',
      okText: 'PNG格式',
      cancelText: 'JPEG格式',
      onOk: () => {
        const result = lineageGraphRef.value.exportAsImage('png', `lineage-graph-${Date.now()}.png`)
        if (result) {
          message.success('PNG图片导出成功')
        } else {
          message.error('PNG图片导出失败')
        }
      },
      onCancel: () => {
        const result = lineageGraphRef.value.exportAsImage('jpeg', `lineage-graph-${Date.now()}.jpg`)
        if (result) {
          message.success('JPEG图片导出成功')
        } else {
          message.error('JPEG图片导出失败')
        }
      }
    })
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleZoomIn = () => {
  console.log('放大')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.zoomIn()
    const currentZoom = lineageGraphRef.value.getCurrentZoom()
    message.success(`已放大，当前缩放比例: ${(currentZoom * 100).toFixed(0)}%`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

const handleZoomOut = () => {
  console.log('缩小')
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    lineageGraphRef.value.zoomOut()
    const currentZoom = lineageGraphRef.value.getCurrentZoom()
    message.success(`已缩小，当前缩放比例: ${(currentZoom * 100).toFixed(0)}%`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

// 布局方向变更处理
const handleLayoutDirectionChange = ({ key }: { key: string }) => {
  console.log('切换布局方向:', key)
  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    const directionMap = {
      'LR': '从左到右',
      'TB': '从上到下',
      'RL': '从右到左',
      'BT': '从下到上'
    }

    lineageGraphRef.value.changeLayoutDirection(key as 'LR' | 'TB' | 'RL' | 'BT')
    message.success(`布局方向已切换为: ${directionMap[key as keyof typeof directionMap]}`)
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

// 性能模式变更处理
const handlePerformanceModeChange = (value: string | { key: string }) => {
  const key = typeof value === 'string' ? value : value.key
  console.log('切换性能模式:', key)

  const modeMap = {
    'normal': '标准模式',
    'optimized': '优化模式',
    'extreme': '极速模式'
  }

  currentPerformanceMode.value = key as 'normal' | 'optimized' | 'extreme'

  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    // 更新图谱的性能模式
    lineageGraphRef.value.performanceMode.value = key as 'normal' | 'optimized' | 'extreme'

    // 根据性能模式调整相关设置
    switch (key) {
      case 'normal':
        lineageGraphRef.value.virtualRenderingEnabled.value = false
        lineageGraphRef.value.lazyLoadingEnabled.value = false
        break
      case 'optimized':
        lineageGraphRef.value.virtualRenderingEnabled.value = true
        lineageGraphRef.value.lazyLoadingEnabled.value = false
        break
      case 'extreme':
        lineageGraphRef.value.virtualRenderingEnabled.value = true
        lineageGraphRef.value.lazyLoadingEnabled.value = true
        break
    }

    // 重新渲染图谱以应用新的性能设置
    if (lineageStore.g6GraphData) {
      lineageGraphRef.value.setGraphData(lineageStore.g6GraphData)
    }

    message.success(`性能模式已切换为: ${modeMap[key as keyof typeof modeMap]}`)

    // 更新性能统计
    updatePerformanceStats()
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

// 更新性能统计
const updatePerformanceStats = () => {
  if (lineageStore.g6GraphData) {
    performanceStats.nodeCount = lineageStore.g6GraphData.nodes.length
    performanceStats.edgeCount = lineageStore.g6GraphData.edges.length
  }

  // 获取内存使用情况（如果支持）
  if ('memory' in performance) {
    const memInfo = (performance as any).memory
    performanceStats.memoryUsage = Math.round(memInfo.usedJSHeapSize / 1024 / 1024) // MB
  }
}

const handleLoadSample = async () => {
  try {
    console.log('加载示例数据')
    await lineageStore.loadSampleData()
    message.success('示例数据加载完成')

    // 运行G6示例数据测试
    setTimeout(() => {
      console.log('🔍 检查G6示例数据结构:')
      console.log('G6图数据:', lineageStore.g6GraphData)

      if (lineageStore.g6GraphData) {
        // 检查节点数据
        console.log('📊 节点数据统计:', {
          总节点数: lineageStore.g6GraphData.nodes.length,
          带combo的节点: lineageStore.g6GraphData.nodes.filter(node => node.comboId).length,
          combo类型: [...new Set(lineageStore.g6GraphData.nodes.map(node => node.comboId).filter(Boolean))]
        })

        // 检查combo数据
        if (lineageStore.g6GraphData.combos) {
          console.log('🏷️ Combo数据:', lineageStore.g6GraphData.combos.map(combo => ({
            id: combo.id,
            label: combo.label,
            type: combo.type
          })))
        } else {
          console.log('⚠️ 未发现combo数据')
        }

        // 检查边数据
        console.log('🔗 边数据统计:', {
          总边数: lineageStore.g6GraphData.edges.length,
          转换类型: [...new Set(lineageStore.g6GraphData.edges.map(edge => edge.lineageEdge?.transformType).filter(Boolean))]
        })

        // 显示前3条边的详细信息
        console.log('边数据详情（前3条）:')
        lineageStore.g6GraphData.edges.slice(0, 3).forEach((edge, index) => {
          console.log(`边 ${index + 1}:`, {
            id: edge.id,
            source: edge.source,
            target: edge.target,
            type: edge.type,
            sourceField: edge.sourceField,
            targetField: edge.targetField,
            transformType: edge.lineageEdge?.transformType,
            label: edge.label,
            confidence: edge.lineageEdge?.confidence
          })
        })

        // 检查血缘数据
        if (lineageStore.lineageData) {
          console.log('📋 血缘数据统计:', {
            表数量: Object.keys(lineageStore.lineageData.tables).length,
            字段节点数: lineageStore.lineageData.nodes.length,
            血缘边数: lineageStore.lineageData.edges.length,
            版本: lineageStore.lineageData.metadata?.version,
            描述: lineageStore.lineageData.metadata?.description
          })

          // 显示表的combo信息
          const tablesWithCombo = Object.values(lineageStore.lineageData.tables).filter(table => table.combo)
          if (tablesWithCombo.length > 0) {
            console.log('🏢 表的分层信息:', tablesWithCombo.map(table => ({
              表名: table.name,
              分层: table.combo,
              模式: table.schema,
              类型: table.type
            })))
          }
        }
      }
    }, 500)
  } catch (error) {
    handleError(error, ErrorType.SYSTEM, {
      showNotification: true
    })
  }
}

// 错误处理相关方法
const handleGraphError = (error: Error, errorInfo: any) => {
  console.error('图谱组件错误:', error, errorInfo)

  // 检查是否是事件边界错误
  if (error.message && error.message.includes('EventBoundary')) {
    console.warn('检测到G6事件边界错误，尝试重新初始化图谱...')
    // 延迟重新初始化图谱以避免事件冲突
    setTimeout(() => {
      if (lineageGraphRef.value) {
        lineageGraphRef.value.destroyGraph()
        setTimeout(async () => {
          if (lineageStore.g6GraphData) {
            await lineageGraphRef.value?.setGraphData(lineageStore.g6GraphData)
          }
        }, 100)
      }
    }, 200)
    return
  }

  handleError(error, ErrorType.SYSTEM, {
    showNotification: true,
    context: { component: 'LineageGraph', errorInfo }
  })
}

const handleGraphRetry = () => {
  if (lineageStore.g6GraphData) {
    // 重新渲染图谱
    lineageGraphRef.value?.resetGraph()
    message.info('正在重新渲染图谱...')
  }
}

const handleCancelLoading = () => {
  // 取消当前加载操作
  lineageStore.setLoading(false)
  message.info('操作已取消')
}

const handleRetryOperation = () => {
  lineageStore.retryLastOperation()
}

const handleReportError = (errorInfo: any) => {
  console.log('报告错误:', errorInfo)
  // 这里可以集成错误报告服务
  message.success('错误报告已发送')
}

const handleDismissError = () => {
  lineageStore.clearError()
}

const handleEmptyAction = (action: any) => {
  console.log('空状态操作:', action)
  if (action.handler) {
    action.handler()
  }
}

// 搜索相关方法
const handleSearch = () => {
  lineageStore.setSearchKeyword(searchKeyword.value)
}

const handleSearchEnter = () => {
  if (searchResults.value.length > 0) {
    handleSearchResultClick(searchResults.value[0])
  }
}

const handleSearchResultClick = (result: any) => {
  console.log('搜索结果点击:', result)

  if (lineageGraphRef.value && lineageGraphRef.value.isReady) {
    // 使用图谱组件的定位功能
    const success = lineageGraphRef.value.locateToNode(result.id, true)
    if (success) {
      message.success(`已定位到${result.type === 'table' ? '表' : '字段'}: ${result.name}`)
      // 清空搜索关键词以隐藏搜索结果
      searchKeyword.value = ''
    } else {
      message.error(`无法定位到${result.type === 'table' ? '表' : '字段'}: ${result.name}`)
    }
  } else {
    message.warning('图谱未就绪，请稍后再试')
  }
}

// 图谱事件处理方法
const handleGraphReady = (graph: any) => {
  console.log('图谱初始化完成:', graph)
  message.success('图谱加载完成')
}

const handleNodeClick = (nodeId: string, nodeData: any) => {
  console.log('节点点击:', nodeId, nodeData)
  message.info(`点击了节点: ${nodeData?.label || nodeId}`)
  // 这里可以实现节点详情展示等功能
}

const handleEdgeClick = (edgeId: string, edgeData: any) => {
  console.log('边点击:', edgeId, edgeData)
  message.info(`点击了连线: ${edgeData?.label || edgeId}`)
  // 这里可以实现连线详情展示等功能
}

// 字段事件处理方法
const handleFieldHover = (fieldId: string, fieldData: any, event: any) => {
  console.log('字段悬浮:', fieldId, fieldData)
  // 更新状态管理中的悬浮字段
  lineageStore.setHoveredNode(fieldId)

  // 可以在这里触发Tooltip显示
  // TODO: 实现字段Tooltip组件
}

const handleFieldClick = (fieldId: string, fieldData: any, event: any) => {
  console.log('字段点击:', fieldId, fieldData)
  message.info(`点击了字段: ${fieldData?.fieldName || fieldId}`)

  // 更新状态管理中的选中字段
  lineageStore.selectNode(fieldId)

  // 显示字段详情面板
  selectedFieldData.value = fieldData
  showFieldDetail.value = true

  // 高亮字段和相关连线
  if (lineageGraphRef.value) {
    lineageGraphRef.value.setFieldHighlight(fieldId, true)
  }
}

const handleFieldLeave = (fieldId: string) => {
  console.log('字段离开:', fieldId)
  // 清除悬浮状态
  lineageStore.setHoveredNode(null)
}

const handleCanvasClick = () => {
  console.log('画布点击')
  // 清除选中状态等
  lineageStore.clearSelection()
  showFieldDetail.value = false
  selectedFieldData.value = null

  // 清除所有高亮
  if (lineageGraphRef.value) {
    // 清除字段高亮
    if (lineageStore.hoveredNode) {
      lineageGraphRef.value.setFieldHighlight(lineageStore.hoveredNode, false)
    }
  }
}

// 字段详情面板事件处理
const handleTraceLineage = (fieldData: LineageNode) => {
  console.log('追踪字段血缘:', fieldData)
  message.success(`开始追踪字段 ${fieldData.fieldName} 的血缘路径`)

  // 实现路径追踪高亮功能
  if (lineageGraphRef.value) {
    // 高亮字段和所有相关连线
    lineageGraphRef.value.setFieldHighlight(fieldData.id, true)
    lineageGraphRef.value.highlightRelatedEdges(fieldData.id, true)

    // 3秒后清除高亮
    setTimeout(() => {
      if (lineageGraphRef.value) {
        lineageGraphRef.value.setFieldHighlight(fieldData.id, false)
        lineageGraphRef.value.highlightRelatedEdges(fieldData.id, false)
      }
    }, 3000)
  }
}

const handleHighlightField = (fieldData: LineageNode) => {
  console.log('高亮字段:', fieldData)
  message.success(`字段 ${fieldData.fieldName} 已高亮`)

  if (lineageGraphRef.value) {
    lineageGraphRef.value.setFieldHighlight(fieldData.id, true)

    // 2秒后清除高亮
    setTimeout(() => {
      if (lineageGraphRef.value) {
        lineageGraphRef.value.setFieldHighlight(fieldData.id, false)
      }
    }, 2000)
  }
}

const handleRelatedFieldClick = (fieldData: LineageNode) => {
  console.log('点击相关字段:', fieldData)
  // 切换到新的字段详情
  selectedFieldData.value = fieldData
  message.info(`切换到字段: ${fieldData.fieldName}`)
}

// 字段筛选相关方法
const filterTableOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

const applyFieldFilter = () => {
  console.log('应用字段筛选:', fieldFilter)
  updateFieldStats()

  // 调用图谱组件的筛选方法
  if (lineageGraphRef.value) {
    lineageGraphRef.value.applyFieldFilter({
      dataTypes: fieldFilter.dataTypes,
      attributes: fieldFilter.attributes,
      tables: fieldFilter.tables,
      fieldNamePattern: fieldFilter.fieldNamePattern
    })
  }

  // 更新状态管理中的筛选配置
  lineageStore.applyFieldFilter({
    dataTypes: fieldFilter.dataTypes,
    attributes: fieldFilter.attributes,
    tables: fieldFilter.tables,
    fieldNamePattern: fieldFilter.fieldNamePattern
  })

  showFieldFilter.value = false
  message.success(`已应用字段筛选，显示 ${filteredFieldCount.value} 个字段`)
}

const cancelFieldFilter = () => {
  showFieldFilter.value = false
}

const clearFieldFilter = () => {
  fieldFilter.dataTypes = []
  fieldFilter.attributes = []
  fieldFilter.tables = []
  fieldFilter.fieldNamePattern = ''
  updateFieldStats()

  // 清除图谱中的筛选
  if (lineageGraphRef.value) {
    lineageGraphRef.value.clearFieldFilter()
  }

  // 清除状态管理中的筛选配置
  lineageStore.clearFieldFilter()

  message.info('已清除字段筛选')
}

// 主题切换方法
const toggleTheme = () => {
  const newTheme = theme.value === 'light' ? 'dark' : 'light'
  theme.value = newTheme
  lineageStore.setTheme(newTheme)
  message.success(`已切换到${newTheme === 'light' ? '浅色' : '深色'}主题`)
}

const handleThemeChange = (e: any) => {
  const newTheme = e.target.value
  theme.value = newTheme
  lineageStore.setTheme(newTheme)

  // 应用主题到DOM
  configUtils.applyTheme(newTheme)

  // 更新配置管理器
  configManager.updateConfig({ theme: newTheme })

  message.success(`已切换到${newTheme === 'light' ? '浅色' : '深色'}主题`)
}

// 配置变化处理方法
const handleConfigChanged = (config: any) => {
  // 应用主题变化
  if (config.theme !== theme.value) {
    theme.value = config.theme
    lineageStore.setTheme(config.theme)
  }

  // 应用布局变化
  if (config.layoutDirection !== layoutDirection.value) {
    layoutDirection.value = config.layoutDirection
    if (lineageGraphRef.value) {
      lineageGraphRef.value.changeLayoutDirection(config.layoutDirection)
    }
  }

  // 应用显示设置变化
  showFieldTypes.value = config.showFieldTypes
  showTableComments.value = config.showTableComments
  showFieldDescriptions.value = config.showFieldDescriptions
  showDataTypes.value = config.showDataTypes

  // 应用性能设置变化
  performanceMode.value = config.performanceMode
  enableVirtualRendering.value = config.enableVirtualRendering

  // 应用图谱设置变化
  if (config.showFieldLevelLineage !== lineageStore.showFieldLevelLineage) {
    lineageStore.toggleFieldLevelLineage()
  }
  if (config.showCompleteLineage !== lineageStore.showCompleteLineage) {
    lineageStore.toggleCompleteLineage()
  }

  // 应用字段筛选设置
  if (config.fieldFilter) {
    Object.assign(fieldFilter, config.fieldFilter)
    if (config.fieldFilter.enabled) {
      applyFieldFilter()
    }
  }

  console.log('配置已更新:', config)
}

// 配置管理相关方法
const saveCurrentConfig = async () => {
  const config = {
    theme: theme.value as 'light' | 'dark',
    layoutDirection: layoutDirection.value as 'LR' | 'TB' | 'RL' | 'BT',
    showFieldTypes: showFieldTypes.value,
    showTableComments: showTableComments.value,
    showFieldDescriptions: showFieldDescriptions.value,
    showDataTypes: showDataTypes.value,
    performanceMode: performanceMode.value,
    enableVirtualRendering: enableVirtualRendering.value,
    fieldFilter: {
      enabled: false,
      dataTypes: fieldFilter.dataTypes,
      attributes: fieldFilter.attributes,
      tables: fieldFilter.tables,
      fieldNamePattern: fieldFilter.fieldNamePattern
    },
    showFieldLevelLineage: showFieldLevel.value,
    showCompleteLineage: showFullLineage.value
  }

  configManager.updateConfig(config)
  configManager.saveToLocalStorage()
  message.success('配置已保存到本地')
}

const loadConfigFromLocal = () => {
  configManager.loadFromLocalStorage()
  const config = configManager.getConfig()

  // 应用配置到组件状态
  theme.value = config.theme
  layoutDirection.value = config.layoutDirection
  showFieldTypes.value = config.showFieldTypes
  showTableComments.value = config.showTableComments
  showFieldDescriptions.value = config.showFieldDescriptions
  showDataTypes.value = config.showDataTypes
  performanceMode.value = config.performanceMode
  enableVirtualRendering.value = config.enableVirtualRendering

  // 应用字段筛选配置
  Object.assign(fieldFilter, config.fieldFilter)

  // 应用主题
  lineageStore.setTheme(config.theme)
  configUtils.applyTheme(config.theme)

  message.success('已加载本地配置')
}

const exportConfig = () => {
  saveCurrentConfig()
  configManager.exportConfig()
  message.success('配置已导出')
}

const importConfig = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const success = await configManager.importConfig(file)
      if (success) {
        loadConfigFromLocal()
        message.success('配置导入成功')
      } else {
        message.error('配置导入失败，请检查文件格式')
      }
    }
  }
  input.click()
}

// 响应式处理
const handleResize = () => {
  // 处理窗口大小变化
  // 在新的布局中，宽度由CSS控制，这里主要用于其他响应式逻辑
  if (window.innerWidth < 768) {
    // 小屏幕时可能需要的其他调整
    leftPanelWidth.value = 300
  } else {
    // 大屏幕时使用40%的比例，这里设置一个基准值用于Ant Design组件
    leftPanelWidth.value = Math.floor(window.innerWidth * 0.4)
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化时调用一次

  // 添加全局错误处理器来捕获G6事件错误
  const originalConsoleError = console.error
  console.error = (...args) => {
    const errorMessage = args.join(' ')
    if (errorMessage.includes('EventBoundary') || errorMessage.includes('freeEvent')) {
      console.warn('捕获到G6事件边界错误，已忽略:', errorMessage)
      return
    }
    originalConsoleError.apply(console, args)
  }

  // 初始化字段统计
  updateFieldStats()

  // 加载保存的配置
  loadConfigFromLocal()

  // 运行字段级连线功能测试
  setTimeout(() => {
    console.log('🧪 运行字段级连线功能测试...')
    runFieldEdgeTests()

    // 运行详细的单元测试
    console.log('\n🔬 运行详细单元测试...')
    runAllFieldEdgeTests()
  }, 1000)

  // 将测试函数暴露到全局，方便在浏览器控制台中调用
  ;(window as any).testFieldEdges = () => {
    console.log('🔍 手动测试字段级连线功能')
    const testResult = runAllFieldEdgeTests()
    console.log('测试结果:', testResult)
    return testResult
  }

  // 暴露演示函数
  ;(window as any).demoFieldEdges = () => {
    console.log('🎯 字段级连线功能演示')
    const demoResult = runCompleteDemo()
    console.log('演示结果:', demoResult)
    return demoResult
  }

  // 暴露字段交互演示函数
  ;(window as any).demoFieldInteraction = () => {
    console.log('🎮 字段交互功能演示')
    const demoResult = runFieldInteractionDemo()
    console.log('演示结果:', demoResult)
    return demoResult
  }

  ;(window as any).demoFieldEvents = () => {
    console.log('🎭 字段交互事件演示')
    demoFieldInteractionEvents()
  }

  ;(window as any).demoFieldHighlight = () => {
    console.log('🌟 字段高亮功能演示')
    const result = demoFieldHighlight()
    console.log('高亮演示结果:', result)
    return result
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Modern clean layout with soft blue and orange accents */
.lineage-layout * {
  box-sizing: border-box;
}

.lineage-layout {
  height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  background: var(--color-background);
}

/* Reset Ant Design Layout component styles */
.lineage-layout :deep(.ant-layout) {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: var(--color-background);
}

.lineage-layout :deep(.ant-layout-sider) {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.layout-container {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  display: flex !important;
  flex-direction: row !important;
  background: var(--color-background);
}

/* Left panel - Code editing area with soft background */
.left-panel {
  border-right: 1px solid var(--color-border-light);
  background: var(--color-background-panel);
  width: var(--left-panel-width) !important;
  max-width: var(--left-panel-width) !important;
  min-width: var(--left-panel-width) !important;
  flex: 0 0 var(--left-panel-width) !important;
  box-sizing: border-box;
  box-shadow: 2px 0 8px var(--color-shadow-light);
}

.left-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: var(--panel-padding);
  box-sizing: border-box;
  margin: 0;
  background: var(--color-background-panel);
}

.toolbar-section {
  flex-shrink: 0;
  margin-bottom: var(--spacing-lg);
  background: var(--color-surface);
  border-radius: var(--border-radius-large);
  padding: var(--content-padding);
  box-shadow: 0 1px 3px var(--color-shadow-light);
}

.toolbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
}

.toolbar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heading);
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.collapse-btn {
  padding: var(--spacing-xs);
  color: var(--color-text-secondary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapse-btn:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-subtle);
}

.control-group {
  margin-bottom: var(--spacing-lg);
}

.control-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-secondary);
}

/* Editor area - Clean and modern */
.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: var(--color-surface);
  border-radius: var(--border-radius-large);
  padding: var(--content-padding);
  box-shadow: 0 1px 3px var(--color-shadow-light);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border-light);
}

.editor-container {
  flex: 1;
  min-height: 0;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  border: 1px solid var(--color-border-light);
}

.sql-editor {
  height: 100% !important;
  font-family: 'JetBrains Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  background: var(--color-surface) !important;
}

/* Right panel - Visualization area with pure white background */
.right-layout {
  background: var(--color-background);
  width: var(--right-panel-width) !important;
  max-width: var(--right-panel-width) !important;
  min-width: var(--right-panel-width) !important;
  flex: 0 0 var(--right-panel-width) !important;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.right-header {
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border-light);
  padding: 0 var(--spacing-xxl);
  height: 64px;
  line-height: 64px;
  box-shadow: 0 1px 3px var(--color-shadow-light);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-heading);
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-right .control-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-right .control-item label {
  font-size: 14px;
  color: var(--color-text-secondary);
  white-space: nowrap;
  font-weight: 500;
}

/* Graph content area - Clean white background */
.graph-content {
  padding: 0;
  background: var(--color-background);
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.graph-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--color-background);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.graph-canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Search box - Modern and clean */
.graph-search {
  position: absolute;
  top: var(--spacing-lg);
  left: var(--spacing-lg);
  z-index: 10;
  width: 320px;
}

.search-input {
  box-shadow: 0 4px 12px var(--color-shadow-medium);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--color-border-light);
  background: var(--color-surface);
}

.search-input:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 4px 12px var(--color-shadow-medium), 0 0 0 2px var(--color-primary-subtle);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-large);
  box-shadow: 0 8px 24px var(--color-shadow-heavy);
  max-height: 240px;
  overflow-y: auto;
  margin-top: var(--spacing-xs);
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  border-bottom: 1px solid var(--color-border-light);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-result-item:hover {
  background-color: var(--color-primary-subtle);
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-type {
  flex-shrink: 0;
  width: 36px;
  height: 22px;
  background: var(--color-primary);
  color: var(--color-surface);
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 22px;
  border-radius: var(--border-radius-small);
  margin-right: var(--spacing-md);
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.result-desc {
  font-size: 12px;
  color: var(--color-text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Floating toolbar - Clean and modern */
.graph-toolbar {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.graph-toolbar .ant-btn {
  box-shadow: 0 4px 12px var(--color-shadow-medium);
  border: 1px solid var(--color-border-light);
  background: var(--color-surface);
  color: var(--color-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.graph-toolbar .ant-btn:hover {
  background: var(--color-primary);
  color: var(--color-surface);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px var(--color-shadow-heavy);
}

.graph-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
}

.placeholder-content {
  text-align: center;
  color: var(--color-text-tertiary);
  max-width: 400px;
  padding: var(--spacing-xxl);
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-xl);
  color: var(--color-border-hover);
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-secondary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.placeholder-content h3 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-heading);
}

.placeholder-content p {
  margin: 0 0 var(--spacing-xl) 0;
  font-size: 14px;
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.placeholder-actions {
  margin-top: var(--spacing-xl);
}

/* Loading state - Clean overlay */
.graph-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-content {
  width: 120px;
  height: 120px;
}

/* Settings drawer - Modern and organized */
.settings-content {
  padding: var(--spacing-sm) 0;
}

.setting-group {
  margin-bottom: var(--spacing-xxl);
  background: var(--color-surface);
  border-radius: var(--border-radius-large);
  padding: var(--content-padding);
  border: 1px solid var(--color-border-light);
}

.setting-group h4 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heading);
  border-bottom: 1px solid var(--color-border-light);
  padding-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm) 0;
}

.setting-item label {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* Field filter dialog - Clean and functional */
.field-filter-content {
  padding: var(--spacing-sm) 0;
}

.filter-section {
  margin-bottom: var(--spacing-xxl);
  background: var(--color-surface);
  border-radius: var(--border-radius-medium);
  padding: var(--content-padding);
  border: 1px solid var(--color-border-light);
}

.filter-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
}

.filter-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-checkbox-group .ant-checkbox-wrapper {
  margin: 0;
  padding: var(--spacing-xs) 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-checkbox-group .ant-checkbox-wrapper:hover {
  background-color: var(--color-primary-subtle);
  border-radius: var(--border-radius-small);
  padding-left: var(--spacing-sm);
}

.filter-help {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--color-surface-variant);
  border-radius: var(--border-radius-small);
}

.filter-help small {
  color: var(--color-text-tertiary);
  font-size: 12px;
  line-height: 1.4;
}

.filter-stats {
  display: flex;
  gap: var(--spacing-xxl);
  justify-content: center;
}

.filter-stats .ant-statistic {
  text-align: center;
  background: var(--color-surface-variant);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-medium);
}

/* Responsive design - Maintain clean layout on all devices */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column !important;
  }

  .left-panel {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    flex: 0 0 auto !important;
    height: auto !important;
    max-height: 45vh;
    overflow-y: auto;
    box-shadow: 0 2px 8px var(--color-shadow-light);
  }

  .right-layout {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    flex: 1 1 auto !important;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
    padding: var(--spacing-md) 0;
    gap: var(--spacing-sm);
  }

  .header-left {
    margin-bottom: var(--spacing-sm);
  }

  .header-right .control-item {
    font-size: 12px;
  }

  .right-header {
    height: auto;
    line-height: normal;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .graph-search {
    width: calc(100% - 32px);
    left: var(--spacing-lg);
    right: var(--spacing-lg);
  }

  .graph-toolbar {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    top: auto;
    flex-direction: row;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 576px) {
  .left-panel-content {
    padding: var(--spacing-md);
  }

  .toolbar-header h3 {
    font-size: 14px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .ant-space {
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-xs) !important;
  }

  .setting-group {
    padding: var(--spacing-md);
  }

  .filter-section {
    padding: var(--spacing-md);
  }
}

/* Ensure 4:6 ratio on large screens with enhanced visual hierarchy */
@media (min-width: 769px) {
  .layout-container {
    flex-direction: row !important;
  }

  .left-panel {
    width: var(--left-panel-width) !important;
    max-width: var(--left-panel-width) !important;
    min-width: var(--panel-min-width) !important;
    flex: 0 0 var(--left-panel-width) !important;
  }

  .right-layout {
    width: var(--right-panel-width) !important;
    max-width: var(--right-panel-width) !important;
    min-width: calc(100% - var(--panel-min-width)) !important;
    flex: 0 0 var(--right-panel-width) !important;
  }

  /* Enhanced hover effects for desktop */
  .control-group:hover {
    background: var(--color-surface);
    border-radius: var(--border-radius-medium);
    padding: var(--spacing-sm);
    margin: 0 calc(-1 * var(--spacing-sm)) var(--spacing-lg) calc(-1 * var(--spacing-sm));
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .header-right .ant-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--color-shadow-medium);
  }
}
</style>
